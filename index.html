<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Envato Elements GetLink</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .container {
        background: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 600px;
      }

      .logo {
        text-align: center;
        margin-bottom: 30px;
      }

      .logo h1 {
        color: #333;
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .logo p {
        color: #666;
        font-size: 1.1em;
      }

      .form-group {
        margin-bottom: 25px;
      }

      label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 600;
      }

      .input-container {
        position: relative;
      }

      input[type="url"] {
        width: 100%;
        padding: 15px 20px;
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        font-size: 16px;
        transition: all 0.3s ease;
        outline: none;
      }

      input[type="url"]:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .btn:active {
        transform: translateY(0);
      }

      .result {
        margin-top: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        display: none;
      }

      .result.show {
        display: block;
        animation: slideIn 0.3s ease;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .download-link {
        display: inline-block;
        padding: 10px 20px;
        background: #28a745;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        margin-top: 10px;
        transition: background 0.3s ease;
      }

      .download-link:hover {
        background: #218838;
      }

      .loading {
        display: none;
        text-align: center;
        margin-top: 20px;
      }

      .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error {
        color: #dc3545;
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
      }

      .success {
        color: #155724;
        background: #d4edda;
        border: 1px solid #c3e6cb;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">
        <h1>🎨 Envato GetLink</h1>
        <p>Phân tích link Envato Elements</p>
        <div
          class="warning"
          style="
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
            font-size: 0.9em;
          "
        >
          <strong>⚠️ Lưu ý:</strong> Đây chỉ là demo phân tích link. Để download
          hợp pháp, vui lòng mua subscription Envato Elements.
        </div>
      </div>

      <form id="getlinkForm">
        <div class="form-group">
          <label for="envatoUrl">Dán link Envato Elements vào đây:</label>
          <div class="input-container">
            <input
              type="url"
              id="envatoUrl"
              name="envatoUrl"
              placeholder="https://elements.envato.com/..."
              required
            />
          </div>
        </div>

        <button type="submit" class="btn" id="getlinkBtn">Get Link</button>
      </form>

      <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>Đang xử lý link...</p>
      </div>

      <div class="result" id="result">
        <h3>Kết quả:</h3>
        <div id="resultContent"></div>
      </div>
    </div>

    <script>
      document
        .getElementById("getlinkForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          const url = document.getElementById("envatoUrl").value;
          const loading = document.getElementById("loading");
          const result = document.getElementById("result");
          const resultContent = document.getElementById("resultContent");
          const btn = document.getElementById("getlinkBtn");

          // Validate URL
          if (!url.includes("elements.envato.com")) {
            resultContent.innerHTML =
              '<div class="error">Vui lòng nhập link Envato Elements hợp lệ!</div>';
            result.classList.add("show");
            return;
          }

          // Show loading
          loading.style.display = "block";
          result.classList.remove("show");
          btn.disabled = true;
          btn.textContent = "Đang xử lý...";

          // Analyze Envato Elements URL
          analyzeEnvatoUrl(url)
            .then((itemData) => {
              // Hide loading
              loading.style.display = "none";
              btn.disabled = false;
              btn.textContent = "Get Link";

              // Show result
              resultContent.innerHTML = `
                        <div class="success">
                            <strong>Phân tích thành công!</strong> Thông tin item:
                        </div>
                        <div style="margin-top: 15px;">
                            <p><strong>🔗 Link gốc:</strong> <a href="${url}" target="_blank">${url}</a></p>
                            <p><strong>🆔 Item ID:</strong> ${itemData.id}</p>
                            <p><strong>📁 Loại:</strong> ${itemData.category}</p>
                            <p><strong>📊 Thông tin:</strong> ${itemData.info}</p>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196f3;">
                            <h4 style="margin: 0 0 10px 0; color: #1976d2;">💡 Cách download hợp pháp:</h4>
                            <ol style="margin: 0; padding-left: 20px;">
                                <li>Đăng ký <a href="https://elements.envato.com/pricing" target="_blank">Envato Elements subscription</a></li>
                                <li>Đăng nhập vào tài khoản của bạn</li>
                                <li>Truy cập link item và click "Download"</li>
                                <li>Đăng ký license cho project của bạn</li>
                            </ol>
                        </div>

                        <div style="margin-top: 15px; padding: 10px; background: #ffebee; border-radius: 5px; border-left: 4px solid #f44336;">
                            <p style="margin: 0; font-size: 0.9em; color: #c62828;">
                                <strong>⚠️ Cảnh báo:</strong> Download không có subscription vi phạm điều khoản sử dụng của Envato và có thể có hậu quả pháp lý.
                            </p>
                        </div>
                    `;
              result.classList.add("show");
            })
            .catch((error) => {
              // Hide loading
              loading.style.display = "none";
              btn.disabled = false;
              btn.textContent = "Get Link";

              resultContent.innerHTML = `
                        <div class="error">
                            <strong>Lỗi:</strong> ${error.message}
                        </div>
                    `;
              result.classList.add("show");
            });
        });

      async function analyzeEnvatoUrl(url) {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              // Parse URL to extract information
              const urlObj = new URL(url);
              const pathParts = urlObj.pathname
                .split("/")
                .filter((part) => part);

              if (!url.includes("elements.envato.com")) {
                throw new Error("URL không phải từ Envato Elements");
              }

              // Extract item information from URL structure
              // Example: https://elements.envato.com/category/item-name-XXXXXX
              const itemId = pathParts[pathParts.length - 1] || "unknown";
              const category = pathParts[0] || "unknown";

              // Determine category type
              const categoryMap = {
                photos: "📸 Ảnh Stock",
                graphics: "🎨 Đồ họa",
                templates: "📄 Templates",
                video: "🎬 Video",
                audio: "🎵 Audio",
                fonts: "🔤 Fonts",
                "add-ons": "🔧 Add-ons",
                "web-templates": "🌐 Web Templates",
                "cms-templates": "💻 CMS Templates",
                "presentation-templates": "📊 Presentation Templates",
              };

              const categoryName = categoryMap[category] || `📁 ${category}`;

              // Extract item name from URL
              const itemName =
                itemId.split("-").slice(0, -1).join(" ") || "Unknown Item";

              // Simulate getting additional info
              const itemData = {
                id: itemId,
                name: itemName,
                category: categoryName,
                info: `Item được tìm thấy trong danh mục ${categoryName}`,
                originalUrl: url,
              };

              resolve(itemData);
            } catch (error) {
              reject(new Error("Không thể phân tích URL: " + error.message));
            }
          }, 1500); // Simulate API delay
        });
      }

      function extractItemId(url) {
        // Extract item ID from Envato URL
        const match = url.match(/\/([^\/]+)$/);
        return match ? match[1] : "unknown";
      }

      // Auto-focus on input
      document.getElementById("envatoUrl").focus();
    </script>
  </body>
</html>
